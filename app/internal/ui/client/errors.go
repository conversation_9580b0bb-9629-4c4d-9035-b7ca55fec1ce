package client

import (
	"encoding/json"
	"net/http"

	"github.com/information-sharing-networks/signalsd/app/internal/apperrors"
)

// ClientError represents an error from the HTTP client
// StatusCode 0 = network/connection error, >0 = HTTP response received
type ClientError struct {
	StatusCode int                 `json:"status_code"`
	ErrorCode  apperrors.ErrorCode `json:"error_code,omitempty"`
	Message    string              `json:"message"` // User-friendly message for display
}

func (e *ClientError) Error() string {
	return e.Message
}

// NewNetworkError creates a ClientError for network/connection issues
func NewNetworkError(err error) *ClientError {
	return &ClientError{
		StatusCode: 0,
		Message:    "Unable to connect. Please check your internet connection and try again.",
	}
}

// NewHTTPError creates a ClientError from an HTTP response
func NewHTTPError(res *http.Response) *ClientError {
	// Try to parse server error response
	var serverErr struct {
		ErrorCode string `json:"error_code"`
		Message   string `json:"message"`
	}
	if res.Body != nil {
		json.NewDecoder(res.Body).Decode(&serverErr)
	}

	var userMsg string
	switch res.StatusCode {
	case http.StatusUnauthorized:
		userMsg = "Login failed. Please check your email and password and try again."
	case http.StatusForbidden:
		userMsg = "You don't have permission to access this resource."
	case http.StatusBadRequest:
		// Use server message for validation errors if available
		if serverErr.Message != "" {
			userMsg = serverErr.Message
		} else {
			userMsg = "Invalid request. Please check your input and try again."
		}
	case http.StatusTooManyRequests:
		userMsg = "Too many requests. Please try again in a few moments."
	case http.StatusInternalServerError, http.StatusBadGateway, http.StatusServiceUnavailable:
		userMsg = "The service is temporarily unavailable. Please try again later."
	default:
		userMsg = "An error occurred. Please try again."
	}

	return &ClientError{
		StatusCode: res.StatusCode,
		ErrorCode:  apperrors.ErrorCode(serverErr.ErrorCode),
		Message:    userMsg,
	}
}
