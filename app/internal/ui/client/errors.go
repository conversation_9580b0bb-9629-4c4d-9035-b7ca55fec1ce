package client

import (
	"errors"
	"fmt"
	"net"
	"net/url"
	"syscall"

	"github.com/information-sharing-networks/signalsd/app/internal/server/apierrors"
)

type ClientError struct {
	StatusCode int                 `json:"status_code"` // use 0 for network errors
	ErrorCode  apierrors.ErrorCode `json:"error_code,omitempty"`
	Message    string              `json:"message"`
	Retryable  bool                `json:"retryable"`
}

func (e *ClientError) Error() string {
	return fmt.Sprintf("API error %d: %s", e.StatusCode, e.Message)
}

func (e *ClientError) IsAuthError() bool {
	return e.StatusCode == 401 || e.StatusCode == 403
}

func (e *ClientError) IsNetworkError(err error) bool {
	// Unwrap the error to get to the root cause
	var netErr net.Error
	if errors.As(err, &netErr) {
		return true
	}

	// Check for specific network error types
	var opErr *net.OpError
	if errors.As(err, &opErr) {
		return true
	}

	// Check for DNS errors
	var dnsErr *net.DNSError
	if errors.As(err, &dnsErr) {
		return true
	}

	// Check for URL errors (which often wrap network errors)
	var urlErr *url.Error
	if errors.As(err, &urlErr) {
		return e.IsNetworkError(urlErr.Err)
	}

	// Check for syscall errors that indicate network issues
	if errors.Is(err, syscall.ECONNREFUSED) ||
		errors.Is(err, syscall.ECONNRESET) ||
		errors.Is(err, syscall.ENETUNREACH) ||
		errors.Is(err, syscall.EHOSTUNREACH) {
		return true
	}

	return false
}
