package client

import (
	"errors"
	"fmt"
	"net"
	"net/url"
	"syscall"
)

// APIError represents a structured error from the API client
// This contains raw error information without user-friendly messages
type APIError struct {
	StatusCode int    `json:"status_code"`
	ErrorCode  string `json:"error_code,omitempty"`
	Message    string `json:"message"`
	RequestID  string `json:"request_id,omitempty"`
	Retryable  bool   `json:"retryable"`
}

func (e *APIError) Error() string {
	if e.StatusCode == 0 {
		return fmt.Sprintf("network error: %s", e.Message)
	}
	return fmt.Sprintf("API error %d: %s", e.StatusCode, e.Message)
}

// IsNetworkError returns true if this is a network/connection error
func (e *APIError) IsNetworkError() bool {
	return e.StatusCode == 0
}

// IsAuthError returns true if this is an authentication/authorization error
func (e *APIError) IsAuthError() bool {
	return e.StatusCode == 401 || e.StatusCode == 403
}

// IsValidationError returns true if this is a validation error
func (e *APIError) IsValidationError() bool {
	return e.StatusCode == 400
}

// IsServerError returns true if this is a server error
func (e *APIError) IsServerError() bool {
	return e.StatusCode >= 500
}

// IsRetryable returns true if the error might succeed on retry
func (e *APIError) IsRetryable() bool {
	return e.Retryable
}

// isNetworkError checks if an error is a network-related error
func isNetworkError(err error) bool {
	// Unwrap the error to get to the root cause
	var netErr net.Error
	if errors.As(err, &netErr) {
		return true
	}

	// Check for specific network error types
	var opErr *net.OpError
	if errors.As(err, &opErr) {
		return true
	}

	// Check for DNS errors
	var dnsErr *net.DNSError
	if errors.As(err, &dnsErr) {
		return true
	}

	// Check for URL errors (which often wrap network errors)
	var urlErr *url.Error
	if errors.As(err, &urlErr) {
		return isNetworkError(urlErr.Err)
	}

	// Check for syscall errors that indicate network issues
	if errors.Is(err, syscall.ECONNREFUSED) ||
		errors.Is(err, syscall.ECONNRESET) ||
		errors.Is(err, syscall.ENETUNREACH) ||
		errors.Is(err, syscall.EHOSTUNREACH) {
		return true
	}

	return false
}
